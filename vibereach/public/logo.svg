<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="102.66 199.5 377.75 421.3">     <defs>         <style>             .cls-1 { fill: #e5e5e5; }             .cls-2 { fill: url(#linear-gradient); }             .cls-3 { fill: url(#linear-gradient-2); }             .cls-4 { fill: url(#linear-gradient-3); }             .cls-5 { fill: url(#linear-gradient-4); }         </style>         <linearGradient id="linear-gradient" x1="423.52" y1="341.23" x2="325.99" y2="467.36" gradientTransform="matrix(1, 0, 0, -1, 0, 841.89)" gradientUnits="userSpaceOnUse">             <stop offset="0.01" stop-color="#ebebeb"></stop>             <stop offset="0.04" stop-color="#e0e0e0"></stop>             <stop offset="0.27" stop-color="#b9b9b9"></stop>             <stop offset="0.43" stop-color="#989898"></stop>             <stop offset="0.54" stop-color="#7f7f7f"></stop>             <stop offset="0.73" stop-color="#6d6d6d"></stop>             <stop offset="0.87" stop-color="#626262"></stop>             <stop offset="1" stop-color="#5f5f5f"></stop>         </linearGradient>         <linearGradient id="linear-gradient-2" x1="237.16" y1="231.85" x2="423.04" y2="404.44" gradientTransform="matrix(1, 0, 0, -1, 0, 841.89)" gradientUnits="userSpaceOnUse">             <stop offset="0.01" stop-color="#ebebeb"></stop>             <stop offset="0.04" stop-color="#e0e0e0"></stop>             <stop offset="0.26" stop-color="#b9b9b9"></stop>             <stop offset="0.33" stop-color="#989898"></stop>             <stop offset="0.46" stop-color="#7f7f7f"></stop>             <stop offset="0.58" stop-color="#6d6d6d"></stop>             <stop offset="0.7" stop-color="#626262"></stop>             <stop offset="0.85" stop-color="#5f5f5f"></stop>         </linearGradient>         <linearGradient id="linear-gradient-3" x1="124.63" y1="475.2" x2="275.45" y2="293.71" gradientTransform="matrix(1, 0, 0, -1, 0, 841.89)" gradientUnits="userSpaceOnUse">             <stop offset="0" stop-color="#ededed"></stop>             <stop offset="0.03" stop-color="#e0e0e0"></stop>             <stop offset="0.13" stop-color="#b9b9b9"></stop>             <stop offset="0.39" stop-color="#989898"></stop>             <stop offset="0.54" stop-color="#7f7f7f"></stop>             <stop offset="0.63" stop-color="#6d6d6d"></stop>             <stop offset="0.8" stop-color="#626262"></stop>             <stop offset="0.93" stop-color="#5f5f5f"></stop>         </linearGradient>         <linearGradient id="linear-gradient-4" x1="353.67" y1="596.45" x2="176.15" y2="410.52" gradientTransform="matrix(1, 0, 0, -1, 0, 841.89)" gradientUnits="userSpaceOnUse">             <stop offset="0.01" stop-color="#ededed"></stop>             <stop offset="0.04" stop-color="#e0e0e0"></stop>             <stop offset="0.25" stop-color="#b9b9b9"></stop>             <stop offset="0.37" stop-color="#989898"></stop>             <stop offset="0.5" stop-color="#7f7f7f"></stop>             <stop offset="0.63" stop-color="#6d6d6d"></stop>             <stop offset="0.73" stop-color="#626262"></stop>             <stop offset="0.92" stop-color="#5f5f5f"></stop>         </linearGradient>     </defs>          <g id="Layer_5" data-name="Layer 5">         <path class="cls-2" d="M319.5,348.5l-62.83,58.69a41.45,41.45,0,1,0,66.16,49.73L360.5,418.5s18.28-16.11,33.14,2.44L409.5,485.5l2,58,45.2-44.08A103,103,0,0,0,319.5,348.5" transform="translate(0 0)"></path>     </g>     <g id="Layer_4" data-name="Layer 4">         <path class="cls-3" d="M183,563s76.5,102.5,172.5,34.5l56-54s51.28-60.11-17.86-122.56c0,0,3.46,13.43-6.14,31.56C383,461,301,536,301,536Z" transform="translate(0 0)"></path>     </g>     <g id="Layer_3" data-name="Layer 3">         <path class="cls-4" d="M184.66,304.26S56,396,121,497l62,66s51,36,118-25l35.21-34.6S286,540,233,497c-7.68-6.23-14.67-15.18-20.12-25.1-1.56-2.84-3.62-4.75-5-7.81C180.46,404,184.66,304.26,184.66,304.26Z" transform="translate(0 0)"></path>     </g>     <g id="Layer_2" data-name="Layer 2">         <path class="cls-5" d="M288.5,199.5,168,318s-25.19,19.51-11,69c5.05,17.59,14.25,43.67,35,67.4,6,6.83,31.42,34.43,39.43,41.24,0,0-43.89-64.14-11.89-111.14l95-95s32-33.53,0-64.27Z" transform="translate(0 0)"></path>     </g> </svg>