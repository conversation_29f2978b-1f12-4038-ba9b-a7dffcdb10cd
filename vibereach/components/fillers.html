  <!-- DaisyUI Card Version -->
      <div class="flex justify-center p-4">
        <div class="card w-full max-w-5xl bg-neutral rounded-3xl border border-gray-600/60 p-8 transition-all duration-400 ease-in-out hover:shadow-2xl">
          <div class="card-body p-0">
            <!-- Card Header -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-2xl font-medium text-white">
                <i id="how-it-works" class="fas fa-bullhorn text-primary mr-2"></i>How it works?
              </h3>
            </div>
            
            <!-- Workflow Content -->
            <div class="space-y-6 text-white">
              <!-- Workflow Row -->
              <div class="flex flex-col lg:flex-row items-center gap-5">
                <!-- Step 1 -->
                <div class="flex-1 bg-white/10 rounded-xl p-6 text-center transition-all hover:-translate-y-2 hover:shadow-lg flex flex-col items-center">
                  <i class="fas fa-list-alt text-primary text-3xl mb-4"></i>
                  <h4 class="text-primary text-xl font-medium mb-3">1. Lead List Input</h4>
                  <p class="text-white/80">Basic Info, Emails, Website Links, LinkedIn Profiles</p>
                </div>
                
                <!-- Arrow (hidden on mobile) -->
                <div class="hidden lg:flex items-center justify-center text-white/50 text-2xl">
                  <i class="fas fa-arrow-right"></i>
                </div>
                
                <!-- Sources Column -->
                <div class="flex-2 w-full lg:w-auto">
                  <!-- Input Sources -->
                  <div class="flex flex-col lg:flex-row gap-4 mb-3">
                    <div class="flex-1 bg-white/8 rounded-lg p-4 text-center transition-all hover:-translate-y-1 flex flex-col items-center">
                      <i class="fas fa-user-edit text-primary text-xl mb-2"></i>
                      <p class="text-sm">Email Template</p>
                    </div>
                    
                    <div class="flex-1 bg-white/8 rounded-lg p-4 text-center transition-all hover:-translate-y-1 flex flex-col items-center">
                      <i class="fas fa-database text-primary text-xl mb-2"></i>
                      <p class="text-sm">Lead Info</p>
                    </div>
                    
                    <div class="flex-1 bg-white/8 rounded-lg p-4 text-center transition-all hover:-translate-y-1 flex flex-col items-center">
                      <i class="fas fa-palette text-primary text-xl mb-2"></i>
                      <p class="text-sm">Business Tone & Style</p>
                    </div>
                  </div>
                  
                  <!-- Converging Arrows -->
                  <div class="my-4 h-10 flex justify-center">
                    <svg viewBox="0 0 300 40" class="w-full h-full">
                      <line x1="50" y1="0" x2="150" y2="40" stroke="#d97757" stroke-width="2"/>
                      <line x1="150" y1="0" x2="150" y2="40" stroke="#d97757" stroke-width="2"/>
                      <line x1="250" y1="0" x2="150" y2="40" stroke="#d97757" stroke-width="2"/>
                    </svg>
                  </div>
                  
                  <!-- AI Processor -->
                  <div class="bg-primary/15 rounded-xl p-5 text-center mb-3">
                    <i class="fas fa-robot text-primary text-3xl mb-3"></i>
                    <h4 class="text-primary text-xl font-medium mb-2">2. AI Processing</h4>
                    <p class="text-white/80">Generates custom, personalized message</p>
                  </div>
                </div>
                
                <!-- Arrow (hidden on mobile) -->
                <div class="hidden lg:flex items-center justify-center text-white/50 text-2xl">
                  <i class="fas fa-arrow-right"></i>
                </div>
                
                <!-- Step 3 -->
                <div class="flex-1 bg-white/10 rounded-xl p-6 text-center transition-all hover:-translate-y-2 hover:shadow-lg flex flex-col items-center">
                  <i class="fas fa-paper-plane text-primary text-3xl mb-4"></i>
                  <h4 class="text-primary text-xl font-medium mb-3">3. Send Email</h4>
                  <p class="text-white/80">Deliver relevant outreach to prospect with your business tone & style</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>











      