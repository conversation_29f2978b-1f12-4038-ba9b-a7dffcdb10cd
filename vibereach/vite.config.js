import { defineConfig } from 'vite'

export default defineConfig({
  base: '/static/',
  build: {
    manifest: true,
    outDir: './dist',
    rollupOptions: {
      input: {
        main: './main.js',
      },
    },
    assetsDir: 'assets',
  },
  server: {
    host: 'localhost',
    port: 3000,
    open: false,
    watch: {
      usePolling: true,
      disableGlobbing: false,
    },
    cors: true,
  },
})
